{% extends 'base.html' %}

{% block title %}Department Management - Smart Management System{% endblock %}

{% block content %}
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2"><i class="fas fa-building me-3"></i>Department Management</h1>
                        <p class="lead mb-0">Manage organizational departments</p>
                    </div>
                    <div>
                        <a href="{% url 'core:admin_dashboard' %}" class="btn btn-outline-light me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                        <a href="{% url 'core:department_create' %}" class="btn btn-light">
                            <i class="fas fa-plus me-2"></i>Add Department
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <div class="card">
        <div class="card-body">
            <h5>Department Management</h5>
            <p class="text-muted">This feature is under development. Please use the Django admin panel for now.</p>
            <a href="/admin/core/department/" class="btn btn-primary" target="_blank">
                <i class="fas fa-external-link-alt me-2"></i>Open Admin Panel
            </a>
        </div>
    </div>
</div>
{% endblock %}
